"""
Unit tests for Pydantic schemas
"""

import pytest
from pydantic import ValidationError
import uuid
from datetime import datetime, timezone, date

from app.modules.auth.schemas.user import (
    UserCreate, UserLogin, UserResponse, 
    EmailVerificationRequest, EmailVerificationResponse
)
from app.modules.profile.schemas.user_profile import (
    UserProfileBase, UserProfileUpdate, UserProfileResponse, GenderEnum
)
from app.shared.schemas.response import SuccessResponse, ErrorResponse


class TestAuthSchemas:
    """Test authentication schemas"""
    
    def test_user_create_valid(self):
        """Test valid UserCreate schema"""
        user_data = UserCreate(
            email="<EMAIL>",
            password="StrongPassword123!",
            confirm_password="StrongPassword123!"
        )
        
        assert user_data.email == "<EMAIL>"
        assert user_data.password == "StrongPassword123!"
        assert user_data.confirm_password == "StrongPassword123!"
    
    def test_user_create_password_mismatch(self):
        """Test UserCreate with password mismatch"""
        with pytest.raises(ValidationError) as exc_info:
            UserCreate(
                email="<EMAIL>",
                password="StrongPassword123!",
                confirm_password="DifferentPassword123!"
            )
        
        errors = exc_info.value.errors()
        assert len(errors) > 0
        assert any("password" in str(error) for error in errors)
    
    def test_user_create_invalid_email(self):
        """Test UserCreate with invalid email"""
        with pytest.raises(ValidationError) as exc_info:
            UserCreate(
                email="not-an-email",
                password="StrongPassword123!",
                confirm_password="StrongPassword123!"
            )
        
        errors = exc_info.value.errors()
        assert len(errors) > 0
        assert any("email" in str(error) for error in errors)
    
    def test_user_login_valid(self):
        """Test valid UserLogin schema"""
        login_data = UserLogin(
            email="<EMAIL>",
            password="password123"
        )
        
        assert login_data.email == "<EMAIL>"
        assert login_data.password == "password123"
    
    def test_user_response_valid(self):
        """Test valid UserResponse schema"""
        user_response = UserResponse(
            user_id=uuid.uuid4(),
            email="<EMAIL>",
            display_name="test",
            is_verified=True,
            created_at=datetime.now(timezone.utc),
            full_name="Test User",
            phone="0123456789"
        )
        
        assert user_response.email == "<EMAIL>"
        assert user_response.display_name == "test"
        assert user_response.is_verified == True
        assert user_response.full_name == "Test User"
    
    def test_email_verification_request_valid(self):
        """Test valid EmailVerificationRequest schema"""
        verify_request = EmailVerificationRequest(
            email="<EMAIL>",
            token="sample-token-123"
        )
        
        assert verify_request.email == "<EMAIL>"
        assert verify_request.token == "sample-token-123"
    
    def test_email_verification_response_valid(self):
        """Test valid EmailVerificationResponse schema"""
        verify_response = EmailVerificationResponse(
            message="Email verified successfully",
            is_verified=True
        )
        
        assert verify_response.message == "Email verified successfully"
        assert verify_response.is_verified == True


class TestProfileSchemas:
    """Test profile schemas"""
    
    def test_user_profile_base_valid(self):
        """Test valid UserProfileBase schema"""
        profile = UserProfileBase(
            full_name="John Doe",
            phone="0123456789",
            date_of_birth=date(1990, 1, 1),
            gender=GenderEnum.male,
            address="123 Test Street",
            avatar_url="https://example.com/avatar.jpg"
        )
        
        assert profile.full_name == "John Doe"
        assert profile.phone == "0123456789"
        assert profile.date_of_birth == date(1990, 1, 1)
        assert profile.gender == GenderEnum.male
        assert profile.address == "123 Test Street"
    
    def test_user_profile_base_empty(self):
        """Test UserProfileBase with all optional fields"""
        profile = UserProfileBase()
        
        assert profile.full_name is None
        assert profile.phone is None
        assert profile.date_of_birth is None
        assert profile.gender is None
        assert profile.address is None
    
    def test_user_profile_update_partial(self):
        """Test UserProfileUpdate with partial data"""
        profile_update = UserProfileUpdate(
            full_name="Updated Name",
            phone="0987654321"
        )
        
        assert profile_update.full_name == "Updated Name"
        assert profile_update.phone == "0987654321"
        assert profile_update.address is None
    
    def test_user_profile_response_valid(self):
        """Test valid UserProfileResponse schema"""
        profile_response = UserProfileResponse(
            full_name="John Doe",
            phone="0123456789",
            date_of_birth=date(1990, 1, 1),
            gender=GenderEnum.female,
            address="123 Test Street"
        )
        
        assert profile_response.full_name == "John Doe"
        assert profile_response.gender == GenderEnum.female
    
    def test_gender_enum_values(self):
        """Test GenderEnum values"""
        assert GenderEnum.male == "male"
        assert GenderEnum.female == "female"
        assert GenderEnum.other == "other"
    
    def test_gender_enum_invalid(self):
        """Test invalid gender enum"""
        with pytest.raises(ValidationError):
            UserProfileBase(gender="invalid_gender")


class TestResponseSchemas:
    """Test response schemas"""
    
    def test_success_response_with_data(self):
        """Test SuccessResponse with data"""
        response = SuccessResponse[dict](
            message="Operation successful",
            data={"key": "value"}
        )
        
        assert response.success == True
        assert response.message == "Operation successful"
        assert response.data == {"key": "value"}
        assert response.timestamp is not None
    
    def test_success_response_without_data(self):
        """Test SuccessResponse without data"""
        response = SuccessResponse[None](
            message="Operation successful"
        )
        
        assert response.success == True
        assert response.message == "Operation successful"
        assert response.data is None
    
    def test_error_response_full(self):
        """Test ErrorResponse with all fields"""
        response = ErrorResponse(
            message="Something went wrong",
            error_code="TEST_001",
            error_details={"field": "email", "issue": "already exists"}
        )
        
        assert response.success == False
        assert response.message == "Something went wrong"
        assert response.error_code == "TEST_001"
        assert response.error_details == {"field": "email", "issue": "already exists"}
        assert response.timestamp is not None
    
    def test_error_response_minimal(self):
        """Test ErrorResponse with minimal fields"""
        response = ErrorResponse(message="Simple error")
        
        assert response.success == False
        assert response.message == "Simple error"
        assert response.error_code is None
        assert response.error_details is None


class TestSchemaSerialization:
    """Test schema serialization/deserialization"""
    
    def test_user_create_serialization(self):
        """Test UserCreate serialization"""
        user_create = UserCreate(
            email="<EMAIL>",
            password="StrongPassword123!",
            confirm_password="StrongPassword123!"
        )
        
        # Test dict conversion
        user_dict = user_create.model_dump()
        assert "email" in user_dict
        assert "password" in user_dict
        assert "confirm_password" in user_dict
        
        # Test JSON conversion
        user_json = user_create.model_dump_json()
        assert isinstance(user_json, str)
        assert "<EMAIL>" in user_json
    
    def test_user_response_from_attributes(self):
        """Test UserResponse from_attributes functionality"""
        # Simulate database row
        class MockUser:
            user_id = uuid.uuid4()
            email = "<EMAIL>"
            display_name = "test"
            is_verified = True
            created_at = datetime.now(timezone.utc)
            full_name = "Test User"
            phone = "0123456789"
            avatar_url = None
        
        mock_user = MockUser()
        user_response = UserResponse.model_validate(mock_user)
        
        assert user_response.email == "<EMAIL>"
        assert user_response.display_name == "test"
        assert user_response.full_name == "Test User"
    
    def test_profile_response_serialization(self):
        """Test ProfileResponse serialization"""
        profile_response = UserProfileResponse(
            full_name="John Doe",
            phone="0123456789",
            date_of_birth=date(1990, 1, 1),
            gender=GenderEnum.male,
            address="123 Test Street"
        )
        
        # Test dict conversion
        profile_dict = profile_response.model_dump()
        assert profile_dict["full_name"] == "John Doe"
        assert profile_dict["gender"] == "male"
        assert profile_dict["date_of_birth"] == "1990-01-01"  # Date serialized as string


if __name__ == "__main__":
    pytest.main([__file__])
