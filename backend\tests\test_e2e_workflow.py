"""
End-to-End Integration Tests
Tests complete user workflows from registration to profile management
"""

import pytest
from fastapi.testclient import Test<PERSON><PERSON>
from unittest.mock import patch, MagicMock
import uuid
from datetime import datetime, timezone

from app.main import app
from app.core.database import get_db


@pytest.fixture
def client():
    """Create test client"""
    return TestClient(app)


@pytest.fixture
def mock_db():
    """Mock database dependency"""
    from unittest.mock import AsyncMock
    return AsyncMock()


@pytest.fixture
def override_get_db(mock_db):
    """Override database dependency"""
    def _override_get_db():
        return mock_db
    
    app.dependency_overrides[get_db] = _override_get_db
    yield
    app.dependency_overrides.clear()


class TestCompleteUserWorkflow:
    """Test complete user workflow from registration to profile management"""
    
    def test_user_registration_to_profile_workflow(self, client, override_get_db):
        """Test complete workflow: register → verify → login → update profile → get profile"""
        
        # Step 1: User Registration
        with patch('app.modules.auth.services.auth_service.AuthService.create_user') as mock_create:
            mock_user = MagicMock()
            mock_user.id = uuid.uuid4()
            mock_user.email = "<EMAIL>"
            mock_user.display_name = "newuser"
            mock_user.is_verified = False
            mock_user.created_at = datetime.now(timezone.utc)
            mock_user.profile = None
            mock_create.return_value = mock_user
            
            register_data = {
                "email": "<EMAIL>",
                "password": "SecurePassword123!",
                "confirm_password": "SecurePassword123!"
            }
            
            register_response = client.post("/api/v1/auth/register", json=register_data)
            
            assert register_response.status_code == 201
            register_data = register_response.json()
            assert register_data["success"] is True
            assert register_data["data"]["email"] == "<EMAIL>"
            assert register_data["data"]["is_verified"] is False
        
        # Step 2: Email Verification
        with patch('app.modules.auth.services.auth_service.AuthService.verify_email') as mock_verify:
            mock_verify.return_value = True
            
            verify_data = {
                "email": "<EMAIL>",
                "token": "verification_token_123"
            }
            
            verify_response = client.post("/api/v1/auth/verify-email", json=verify_data)
            
            assert verify_response.status_code == 200
            verify_data = verify_response.json()
            assert verify_data["success"] is True
            assert verify_data["data"]["is_verified"] is True
        
        # Step 3: User Login
        with patch('app.modules.auth.services.auth_service.AuthService.authenticate_user') as mock_auth:
            mock_user_verified = MagicMock()
            mock_user_verified.id = mock_user.id
            mock_user_verified.email = "<EMAIL>"
            mock_user_verified.display_name = "newuser"
            mock_user_verified.is_verified = True  # Now verified
            mock_user_verified.created_at = datetime.now(timezone.utc)
            mock_user_verified.profile = None
            mock_auth.return_value = mock_user_verified
            
            login_data = {
                "email": "<EMAIL>",
                "password": "SecurePassword123!"
            }
            
            login_response = client.post("/api/v1/auth/login", json=login_data)
            
            assert login_response.status_code == 200
            login_data = login_response.json()
            assert login_data["success"] is True
            assert login_data["data"]["email"] == "<EMAIL>"
            assert login_data["data"]["is_verified"] is True
        
        # Step 4: Update Profile
        with patch('app.modules.profile.services.profile_service.ProfileService.update_profile') as mock_update:
            with patch('app.modules.profile.services.profile_service.ProfileService.create_profile_response') as mock_response:
                mock_profile = MagicMock()
                mock_update.return_value = mock_profile
                
                mock_profile_response = {
                    "user_id": str(mock_user.id),
                    "full_name": "John Doe",
                    "phone": "1234567890",
                    "date_of_birth": None,
                    "gender": "male",
                    "address": "123 Main St",
                    "avatar_url": None,
                    "created_at": datetime.now(timezone.utc),
                    "updated_at": datetime.now(timezone.utc)
                }
                mock_response.return_value = mock_profile_response
                
                profile_data = {
                    "full_name": "John Doe",
                    "phone": "1234567890",
                    "gender": "male",
                    "address": "123 Main St"
                }
                
                profile_response = client.put("/api/v1/profile/update", json=profile_data)
                
                assert profile_response.status_code == 200
                profile_data = profile_response.json()
                assert profile_data["success"] is True
                assert profile_data["data"]["full_name"] == "John Doe"
                assert profile_data["data"]["phone"] == "1234567890"
        
        # Step 5: Get Profile
        with patch('app.modules.profile.services.profile_service.ProfileService.get_profile_by_user_id') as mock_get:
            with patch('app.modules.profile.services.profile_service.ProfileService.create_profile_response') as mock_response:
                mock_profile = MagicMock()
                mock_get.return_value = mock_profile
                
                mock_profile_response = {
                    "user_id": str(mock_user.id),
                    "full_name": "John Doe",
                    "phone": "1234567890",
                    "date_of_birth": None,
                    "gender": "male",
                    "address": "123 Main St",
                    "avatar_url": None,
                    "created_at": datetime.now(timezone.utc),
                    "updated_at": datetime.now(timezone.utc)
                }
                mock_response.return_value = mock_profile_response
                
                get_profile_response = client.get("/api/v1/profile/me")
                
                assert get_profile_response.status_code == 200
                get_profile_data = get_profile_response.json()
                assert get_profile_data["success"] is True
                assert get_profile_data["data"]["full_name"] == "John Doe"
                assert get_profile_data["data"]["phone"] == "1234567890"
                assert get_profile_data["data"]["gender"] == "male"
    
    def test_failed_login_workflow(self, client, override_get_db):
        """Test workflow with failed login attempts"""
        
        # Step 1: Register user
        with patch('app.modules.auth.services.auth_service.AuthService.create_user') as mock_create:
            mock_user = MagicMock()
            mock_user.id = uuid.uuid4()
            mock_user.email = "<EMAIL>"
            mock_user.display_name = "testuser"
            mock_user.is_verified = False
            mock_user.created_at = datetime.now(timezone.utc)
            mock_user.profile = None
            mock_create.return_value = mock_user
            
            register_data = {
                "email": "<EMAIL>",
                "password": "TestPassword123!",
                "confirm_password": "TestPassword123!"
            }
            
            register_response = client.post("/api/v1/auth/register", json=register_data)
            assert register_response.status_code == 201
        
        # Step 2: Try to login without verification (should fail)
        with patch('app.modules.auth.services.auth_service.AuthService.authenticate_user') as mock_auth:
            from app.utils.exceptions.base_exceptions import AppBaseException
            from app.utils.constants.error_codes import AUTH_VERIFICATION_REQUIRED
            
            mock_auth.side_effect = AppBaseException(
                message="Account verification required",
                error_code=AUTH_VERIFICATION_REQUIRED
            )
            
            login_data = {
                "email": "<EMAIL>",
                "password": "TestPassword123!"
            }
            
            login_response = client.post("/api/v1/auth/login", json=login_data)
            
            assert login_response.status_code == 200
            login_data = login_response.json()
            assert login_data["success"] is False
            assert login_data["error_code"] == AUTH_VERIFICATION_REQUIRED
        
        # Step 3: Verify email
        with patch('app.modules.auth.services.auth_service.AuthService.verify_email') as mock_verify:
            mock_verify.return_value = True
            
            verify_data = {
                "email": "<EMAIL>",
                "token": "verification_token_123"
            }
            
            verify_response = client.post("/api/v1/auth/verify-email", json=verify_data)
            assert verify_response.status_code == 200
        
        # Step 4: Now login should succeed
        with patch('app.modules.auth.services.auth_service.AuthService.authenticate_user') as mock_auth:
            mock_user_verified = MagicMock()
            mock_user_verified.id = mock_user.id
            mock_user_verified.email = "<EMAIL>"
            mock_user_verified.display_name = "testuser"
            mock_user_verified.is_verified = True
            mock_user_verified.created_at = datetime.now(timezone.utc)
            mock_user_verified.profile = None
            mock_auth.return_value = mock_user_verified
            
            login_data = {
                "email": "<EMAIL>",
                "password": "TestPassword123!"
            }
            
            login_response = client.post("/api/v1/auth/login", json=login_data)
            
            assert login_response.status_code == 200
            login_data = login_response.json()
            assert login_data["success"] is True
            assert login_data["data"]["is_verified"] is True
    
    def test_profile_management_workflow(self, client, override_get_db):
        """Test profile management workflow"""
        
        # Step 1: Get empty profile (should return not found or empty profile)
        with patch('app.modules.profile.services.profile_service.ProfileService.get_profile_by_user_id') as mock_get:
            mock_get.return_value = None
            
            get_response = client.get("/api/v1/profile/me")
            
            assert get_response.status_code == 200
            get_data = get_response.json()
            assert get_data["success"] is False
            assert get_data["error_code"] == "PROFILE_NOT_FOUND"
        
        # Step 2: Create/Update profile
        with patch('app.modules.profile.services.profile_service.ProfileService.update_profile') as mock_update:
            with patch('app.modules.profile.services.profile_service.ProfileService.create_profile_response') as mock_response:
                mock_profile = MagicMock()
                mock_update.return_value = mock_profile
                
                mock_profile_response = {
                    "user_id": str(uuid.uuid4()),
                    "full_name": "Jane Smith",
                    "phone": "**********",
                    "date_of_birth": None,
                    "gender": "female",
                    "address": "456 Oak Ave",
                    "avatar_url": None,
                    "created_at": datetime.now(timezone.utc),
                    "updated_at": datetime.now(timezone.utc)
                }
                mock_response.return_value = mock_profile_response
                
                profile_data = {
                    "full_name": "Jane Smith",
                    "phone": "**********",
                    "gender": "female",
                    "address": "456 Oak Ave"
                }
                
                update_response = client.put("/api/v1/profile/update", json=profile_data)
                
                assert update_response.status_code == 200
                update_data = update_response.json()
                assert update_data["success"] is True
                assert update_data["data"]["full_name"] == "Jane Smith"
        
        # Step 3: Get updated profile
        with patch('app.modules.profile.services.profile_service.ProfileService.get_profile_by_user_id') as mock_get:
            with patch('app.modules.profile.services.profile_service.ProfileService.create_profile_response') as mock_response:
                mock_profile = MagicMock()
                mock_get.return_value = mock_profile
                
                mock_profile_response = {
                    "user_id": str(uuid.uuid4()),
                    "full_name": "Jane Smith",
                    "phone": "**********",
                    "date_of_birth": None,
                    "gender": "female",
                    "address": "456 Oak Ave",
                    "avatar_url": None,
                    "created_at": datetime.now(timezone.utc),
                    "updated_at": datetime.now(timezone.utc)
                }
                mock_response.return_value = mock_profile_response
                
                get_response = client.get("/api/v1/profile/me")
                
                assert get_response.status_code == 200
                get_data = get_response.json()
                assert get_data["success"] is True
                assert get_data["data"]["full_name"] == "Jane Smith"
                assert get_data["data"]["phone"] == "**********"


class TestAPIHealthChecks:
    """Test API health and status endpoints"""
    
    def test_root_endpoint(self, client):
        """Test root endpoint is accessible"""
        response = client.get("/")
        
        assert response.status_code == 200
        data = response.json()
        assert "app" in data
        assert "version" in data
        assert "message" in data
        assert data["message"] == "API is running"
    
    def test_auth_health_check(self, client, override_get_db):
        """Test auth service health check"""
        response = client.get("/api/v1/auth/health")
        
        assert response.status_code == 200
        data = response.json()
        assert data["success"] is True
        assert data["data"]["service"] == "auth"
        assert data["data"]["status"] == "healthy"
        assert "features" in data["data"]


if __name__ == "__main__":
    pytest.main([__file__, "-v"])
