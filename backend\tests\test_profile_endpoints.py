"""
Comprehensive tests for Profile endpoints
Tests all profile-related functionality including edge cases
"""

import pytest
from fastapi.testclient import TestClient
from unittest.mock import patch, MagicMock, AsyncMock
import uuid
from datetime import datetime, timezone, date

from app.main import app
from app.core.database import get_db
from app.utils.exceptions.base_exceptions import AppBaseException
from app.utils.constants.error_codes import USER_NOT_FOUND, USER_INVALID_DATA


@pytest.fixture
def client():
    """Create test client"""
    return TestClient(app)


@pytest.fixture
def mock_db():
    """Mock database dependency"""
    return AsyncMock()


@pytest.fixture
def override_get_db(mock_db):
    """Override database dependency"""
    def _override_get_db():
        return mock_db
    
    app.dependency_overrides[get_db] = _override_get_db
    yield
    app.dependency_overrides.clear()


class TestProfileGet:
    """Test get profile endpoint"""
    
    def test_get_profile_success(self, client, override_get_db):
        """Test successful profile retrieval"""
        with patch('app.modules.profile.services.profile_service.ProfileService.get_profile_by_user_id') as mock_get:
            with patch('app.modules.profile.services.profile_service.ProfileService.create_profile_response') as mock_response:
                # Mock profile data
                mock_profile = MagicMock()
                mock_get.return_value = mock_profile
                
                mock_profile_response = {
                    "user_id": str(uuid.uuid4()),
                    "full_name": "John Doe",
                    "phone": "1234567890",
                    "date_of_birth": date(1990, 1, 1),
                    "gender": "male",
                    "address": "123 Main St",
                    "avatar_url": "https://example.com/avatar.jpg",
                    "created_at": datetime.now(timezone.utc),
                    "updated_at": datetime.now(timezone.utc)
                }
                mock_response.return_value = mock_profile_response
                
                response = client.get("/api/v1/profile/me")
                
                assert response.status_code == 200
                data = response.json()
                assert data["success"] is True
                assert data["message"] == "Lấy profile thành công"
                assert "data" in data
                assert data["data"]["full_name"] == "John Doe"
                assert data["data"]["phone"] == "1234567890"
                assert data["data"]["gender"] == "male"
    
    def test_get_profile_not_found(self, client, override_get_db):
        """Test get profile when profile doesn't exist"""
        with patch('app.modules.profile.services.profile_service.ProfileService.get_profile_by_user_id') as mock_get:
            mock_get.return_value = None
            
            response = client.get("/api/v1/profile/me")
            
            assert response.status_code == 200
            data = response.json()
            assert data["success"] is False
            assert data["message"] == "Profile không tồn tại"
            assert data["error_code"] == "PROFILE_NOT_FOUND"
    
    def test_get_profile_service_error(self, client, override_get_db):
        """Test get profile with service error"""
        with patch('app.modules.profile.services.profile_service.ProfileService.get_profile_by_user_id') as mock_get:
            mock_get.side_effect = Exception("Database connection error")
            
            response = client.get("/api/v1/profile/me")
            
            assert response.status_code == 200
            data = response.json()
            assert data["success"] is False
            assert data["message"] == "Có lỗi xảy ra, vui lòng thử lại"
            assert data["error_code"] == "INTERNAL_ERROR"


class TestProfileUpdate:
    """Test update profile endpoint"""
    
    def test_update_profile_success(self, client, override_get_db):
        """Test successful profile update"""
        with patch('app.modules.profile.services.profile_service.ProfileService.update_profile') as mock_update:
            with patch('app.modules.profile.services.profile_service.ProfileService.create_profile_response') as mock_response:
                # Mock updated profile
                mock_profile = MagicMock()
                mock_update.return_value = mock_profile
                
                mock_profile_response = {
                    "user_id": str(uuid.uuid4()),
                    "full_name": "Jane Smith",
                    "phone": "0987654321",
                    "date_of_birth": date(1995, 5, 15),
                    "gender": "female",
                    "address": "456 Oak Ave",
                    "avatar_url": None,
                    "created_at": datetime.now(timezone.utc),
                    "updated_at": datetime.now(timezone.utc)
                }
                mock_response.return_value = mock_profile_response
                
                profile_data = {
                    "full_name": "Jane Smith",
                    "phone": "0987654321",
                    "date_of_birth": "1995-05-15",
                    "gender": "female",
                    "address": "456 Oak Ave"
                }
                
                response = client.put("/api/v1/profile/update", json=profile_data)
                
                assert response.status_code == 200
                data = response.json()
                assert data["success"] is True
                assert data["message"] == "Cập nhật profile thành công"
                assert "data" in data
                assert data["data"]["full_name"] == "Jane Smith"
                assert data["data"]["phone"] == "0987654321"
                assert data["data"]["gender"] == "female"
    
    def test_update_profile_partial(self, client, override_get_db):
        """Test partial profile update"""
        with patch('app.modules.profile.services.profile_service.ProfileService.update_profile') as mock_update:
            with patch('app.modules.profile.services.profile_service.ProfileService.create_profile_response') as mock_response:
                # Mock updated profile
                mock_profile = MagicMock()
                mock_update.return_value = mock_profile
                
                mock_profile_response = {
                    "user_id": str(uuid.uuid4()),
                    "full_name": "Updated Name",
                    "phone": None,
                    "date_of_birth": None,
                    "gender": None,
                    "address": None,
                    "avatar_url": None,
                    "created_at": datetime.now(timezone.utc),
                    "updated_at": datetime.now(timezone.utc)
                }
                mock_response.return_value = mock_profile_response
                
                # Only update full_name
                profile_data = {
                    "full_name": "Updated Name"
                }
                
                response = client.put("/api/v1/profile/update", json=profile_data)
                
                assert response.status_code == 200
                data = response.json()
                assert data["success"] is True
                assert data["data"]["full_name"] == "Updated Name"
    
    def test_update_profile_invalid_gender(self, client, override_get_db):
        """Test profile update with invalid gender"""
        profile_data = {
            "full_name": "Test User",
            "gender": "invalid_gender"  # Should be male, female, or other
        }
        
        response = client.put("/api/v1/profile/update", json=profile_data)
        
        assert response.status_code == 422
        data = response.json()
        assert "detail" in data
    
    def test_update_profile_invalid_date_format(self, client, override_get_db):
        """Test profile update with invalid date format"""
        profile_data = {
            "full_name": "Test User",
            "date_of_birth": "invalid-date"
        }
        
        response = client.put("/api/v1/profile/update", json=profile_data)
        
        assert response.status_code == 422
        data = response.json()
        assert "detail" in data
    
    def test_update_profile_invalid_phone(self, client, override_get_db):
        """Test profile update with invalid phone format"""
        profile_data = {
            "full_name": "Test User",
            "phone": "invalid-phone-number-too-long-to-be-valid"
        }
        
        response = client.put("/api/v1/profile/update", json=profile_data)
        
        assert response.status_code == 422
        data = response.json()
        assert "detail" in data
    
    def test_update_profile_service_error(self, client, override_get_db):
        """Test profile update with service error"""
        with patch('app.modules.profile.services.profile_service.ProfileService.update_profile') as mock_update:
            mock_update.side_effect = AppBaseException(
                message="Profile update failed",
                error_code=USER_INVALID_DATA
            )
            
            profile_data = {
                "full_name": "Test User"
            }
            
            response = client.put("/api/v1/profile/update", json=profile_data)
            
            assert response.status_code == 200
            data = response.json()
            assert data["success"] is False
            assert data["error_code"] == USER_INVALID_DATA
    
    def test_update_profile_empty_data(self, client, override_get_db):
        """Test profile update with empty data"""
        with patch('app.modules.profile.services.profile_service.ProfileService.update_profile') as mock_update:
            with patch('app.modules.profile.services.profile_service.ProfileService.create_profile_response') as mock_response:
                # Mock updated profile
                mock_profile = MagicMock()
                mock_update.return_value = mock_profile
                
                mock_profile_response = {
                    "user_id": str(uuid.uuid4()),
                    "full_name": None,
                    "phone": None,
                    "date_of_birth": None,
                    "gender": None,
                    "address": None,
                    "avatar_url": None,
                    "created_at": datetime.now(timezone.utc),
                    "updated_at": datetime.now(timezone.utc)
                }
                mock_response.return_value = mock_profile_response
                
                # Empty update data
                profile_data = {}
                
                response = client.put("/api/v1/profile/update", json=profile_data)
                
                assert response.status_code == 200
                data = response.json()
                assert data["success"] is True


class TestProfileValidation:
    """Test profile data validation"""
    
    def test_valid_gender_values(self, client, override_get_db):
        """Test all valid gender values"""
        valid_genders = ["male", "female", "other"]
        
        for gender in valid_genders:
            with patch('app.modules.profile.services.profile_service.ProfileService.update_profile') as mock_update:
                with patch('app.modules.profile.services.profile_service.ProfileService.create_profile_response') as mock_response:
                    mock_profile = MagicMock()
                    mock_update.return_value = mock_profile
                    
                    mock_profile_response = {
                        "user_id": str(uuid.uuid4()),
                        "full_name": "Test User",
                        "phone": None,
                        "date_of_birth": None,
                        "gender": gender,
                        "address": None,
                        "avatar_url": None,
                        "created_at": datetime.now(timezone.utc),
                        "updated_at": datetime.now(timezone.utc)
                    }
                    mock_response.return_value = mock_profile_response
                    
                    profile_data = {
                        "full_name": "Test User",
                        "gender": gender
                    }
                    
                    response = client.put("/api/v1/profile/update", json=profile_data)
                    
                    assert response.status_code == 200
                    data = response.json()
                    assert data["success"] is True
                    assert data["data"]["gender"] == gender
    
    def test_date_of_birth_formats(self, client, override_get_db):
        """Test various date of birth formats"""
        valid_dates = [
            "1990-01-01",
            "1995-12-31",
            "2000-06-15"
        ]
        
        for date_str in valid_dates:
            with patch('app.modules.profile.services.profile_service.ProfileService.update_profile') as mock_update:
                with patch('app.modules.profile.services.profile_service.ProfileService.create_profile_response') as mock_response:
                    mock_profile = MagicMock()
                    mock_update.return_value = mock_profile
                    
                    mock_profile_response = {
                        "user_id": str(uuid.uuid4()),
                        "full_name": "Test User",
                        "phone": None,
                        "date_of_birth": date_str,
                        "gender": None,
                        "address": None,
                        "avatar_url": None,
                        "created_at": datetime.now(timezone.utc),
                        "updated_at": datetime.now(timezone.utc)
                    }
                    mock_response.return_value = mock_profile_response
                    
                    profile_data = {
                        "full_name": "Test User",
                        "date_of_birth": date_str
                    }
                    
                    response = client.put("/api/v1/profile/update", json=profile_data)
                    
                    assert response.status_code == 200
                    data = response.json()
                    assert data["success"] is True


if __name__ == "__main__":
    pytest.main([__file__, "-v"])
