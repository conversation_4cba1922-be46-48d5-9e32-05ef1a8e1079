"""
Comprehensive tests for Authentication endpoints
Tests all auth-related functionality including edge cases
"""

import pytest
from fastapi.testclient import Test<PERSON>lient
from unittest.mock import patch, MagicMock
import uuid
from datetime import datetime, timezone

from app.main import app
from app.core.database import get_db
from app.utils.exceptions.base_exceptions import AppBaseException
from app.utils.constants.error_codes import (
    AUTH_EMAIL_EXISTS, AUTH_INVALID_CREDENTIALS, 
    AUTH_VERIFICATION_REQUIRED, AUTH_PASSWORD_WEAK
)


@pytest.fixture
def client():
    """Create test client"""
    return TestClient(app)


@pytest.fixture
def mock_db():
    """Mock database dependency"""
    from unittest.mock import AsyncMock
    return AsyncMock()


@pytest.fixture
def override_get_db(mock_db):
    """Override database dependency"""
    def _override_get_db():
        return mock_db
    
    app.dependency_overrides[get_db] = _override_get_db
    yield
    app.dependency_overrides.clear()


class TestAuthRegister:
    """Test user registration endpoint"""
    
    def test_register_success(self, client, override_get_db):
        """Test successful user registration"""
        with patch('app.modules.auth.services.auth_service.AuthService.create_user') as mock_create:
            # Mock successful user creation
            mock_user = MagicMock()
            mock_user.id = uuid.uuid4()
            mock_user.email = "<EMAIL>"
            mock_user.display_name = "test"
            mock_user.is_verified = False
            mock_user.created_at = datetime.now(timezone.utc)
            mock_user.profile = None
            mock_create.return_value = mock_user
            
            user_data = {
                "email": "<EMAIL>",
                "password": "TestPassword123!",
                "confirm_password": "TestPassword123!"
            }
            
            response = client.post("/api/v1/auth/register", json=user_data)
            
            assert response.status_code == 201
            data = response.json()
            assert data["success"] is True
            assert data["message"] == "Đăng ký thành công! Vui lòng kiểm tra email để xác thực tài khoản."
            assert "data" in data
            assert data["data"]["email"] == "<EMAIL>"
            assert data["data"]["is_verified"] is False
    
    def test_register_email_exists(self, client, override_get_db):
        """Test registration with existing email"""
        with patch('app.modules.auth.services.auth_service.AuthService.create_user') as mock_create:
            mock_create.side_effect = AppBaseException(
                message="Email is already registered",
                error_code=AUTH_EMAIL_EXISTS
            )
            
            user_data = {
                "email": "<EMAIL>",
                "password": "TestPassword123!",
                "confirm_password": "TestPassword123!"
            }
            
            response = client.post("/api/v1/auth/register", json=user_data)
            
            assert response.status_code == 200
            data = response.json()
            assert data["success"] is False
            assert data["error_code"] == AUTH_EMAIL_EXISTS
    
    def test_register_weak_password(self, client, override_get_db):
        """Test registration with weak password"""
        with patch('app.modules.auth.services.auth_service.AuthService.create_user') as mock_create:
            mock_create.side_effect = AppBaseException(
                message="Password is too weak",
                error_code=AUTH_PASSWORD_WEAK
            )
            
            user_data = {
                "email": "<EMAIL>",
                "password": "weak",
                "confirm_password": "weak"
            }
            
            response = client.post("/api/v1/auth/register", json=user_data)
            
            assert response.status_code == 200
            data = response.json()
            assert data["success"] is False
            assert data["error_code"] == AUTH_PASSWORD_WEAK
            assert "requirements" in data["error_details"]
    
    def test_register_invalid_email(self, client, override_get_db):
        """Test registration with invalid email format"""
        user_data = {
            "email": "invalid-email",
            "password": "TestPassword123!",
            "confirm_password": "TestPassword123!"
        }
        
        response = client.post("/api/v1/auth/register", json=user_data)
        
        assert response.status_code == 422
        data = response.json()
        assert "detail" in data
    
    def test_register_password_mismatch(self, client, override_get_db):
        """Test registration with password mismatch"""
        user_data = {
            "email": "<EMAIL>",
            "password": "TestPassword123!",
            "confirm_password": "DifferentPassword123!"
        }
        
        response = client.post("/api/v1/auth/register", json=user_data)
        
        assert response.status_code == 422
        data = response.json()
        assert "detail" in data
    
    def test_register_missing_fields(self, client, override_get_db):
        """Test registration with missing required fields"""
        user_data = {
            "email": "<EMAIL>"
            # Missing password and confirm_password
        }
        
        response = client.post("/api/v1/auth/register", json=user_data)
        
        assert response.status_code == 422


class TestAuthLogin:
    """Test user login endpoint"""
    
    def test_login_success(self, client, override_get_db):
        """Test successful login"""
        with patch('app.modules.auth.services.auth_service.AuthService.authenticate_user') as mock_auth:
            # Mock successful authentication
            mock_user = MagicMock()
            mock_user.id = uuid.uuid4()
            mock_user.email = "<EMAIL>"
            mock_user.display_name = "test"
            mock_user.is_verified = True
            mock_user.created_at = datetime.now(timezone.utc)
            mock_user.profile = None
            mock_auth.return_value = mock_user
            
            login_data = {
                "email": "<EMAIL>",
                "password": "TestPassword123!"
            }
            
            response = client.post("/api/v1/auth/login", json=login_data)
            
            assert response.status_code == 200
            data = response.json()
            assert data["success"] is True
            assert data["message"] == "Đăng nhập thành công"
            assert "data" in data
            assert data["data"]["email"] == "<EMAIL>"
            assert data["data"]["is_verified"] is True
    
    def test_login_invalid_credentials(self, client, override_get_db):
        """Test login with invalid credentials"""
        with patch('app.modules.auth.services.auth_service.AuthService.authenticate_user') as mock_auth:
            mock_auth.side_effect = AppBaseException(
                message="Invalid email or password",
                error_code=AUTH_INVALID_CREDENTIALS
            )
            
            login_data = {
                "email": "<EMAIL>",
                "password": "WrongPassword123!"
            }
            
            response = client.post("/api/v1/auth/login", json=login_data)
            
            assert response.status_code == 200
            data = response.json()
            assert data["success"] is False
            assert data["error_code"] == AUTH_INVALID_CREDENTIALS
    
    def test_login_unverified_account(self, client, override_get_db):
        """Test login with unverified account"""
        with patch('app.modules.auth.services.auth_service.AuthService.authenticate_user') as mock_auth:
            mock_auth.side_effect = AppBaseException(
                message="Account verification required",
                error_code=AUTH_VERIFICATION_REQUIRED
            )
            
            login_data = {
                "email": "<EMAIL>",
                "password": "TestPassword123!"
            }
            
            response = client.post("/api/v1/auth/login", json=login_data)
            
            assert response.status_code == 200
            data = response.json()
            assert data["success"] is False
            assert data["error_code"] == AUTH_VERIFICATION_REQUIRED
    
    def test_login_invalid_email_format(self, client, override_get_db):
        """Test login with invalid email format"""
        login_data = {
            "email": "invalid-email",
            "password": "TestPassword123!"
        }
        
        response = client.post("/api/v1/auth/login", json=login_data)
        
        assert response.status_code == 422
    
    def test_login_missing_fields(self, client, override_get_db):
        """Test login with missing fields"""
        login_data = {
            "email": "<EMAIL>"
            # Missing password
        }
        
        response = client.post("/api/v1/auth/login", json=login_data)
        
        assert response.status_code == 422


class TestAuthVerifyEmail:
    """Test email verification endpoint"""
    
    def test_verify_email_success(self, client, override_get_db):
        """Test successful email verification"""
        with patch('app.modules.auth.services.auth_service.AuthService.verify_email') as mock_verify:
            mock_verify.return_value = True
            
            verify_data = {
                "email": "<EMAIL>",
                "token": "valid_token_123"
            }
            
            response = client.post("/api/v1/auth/verify-email", json=verify_data)
            
            assert response.status_code == 200
            data = response.json()
            assert data["success"] is True
            assert data["message"] == "Xác thực email thành công"
            assert data["data"]["is_verified"] is True
    
    def test_verify_email_invalid_token(self, client, override_get_db):
        """Test email verification with invalid token"""
        with patch('app.modules.auth.services.auth_service.AuthService.verify_email') as mock_verify:
            mock_verify.side_effect = AppBaseException(
                message="Invalid verification token",
                error_code=AUTH_INVALID_CREDENTIALS
            )
            
            verify_data = {
                "email": "<EMAIL>",
                "token": "invalid_token"
            }
            
            response = client.post("/api/v1/auth/verify-email", json=verify_data)
            
            assert response.status_code == 200
            data = response.json()
            assert data["success"] is False
            assert data["error_code"] == AUTH_INVALID_CREDENTIALS


class TestAuthHealthCheck:
    """Test auth health check endpoint"""
    
    def test_health_check(self, client, override_get_db):
        """Test auth service health check"""
        response = client.get("/api/v1/auth/health")
        
        assert response.status_code == 200
        data = response.json()
        assert data["success"] is True
        assert data["message"] == "Auth service đang hoạt động bình thường"
        assert "data" in data
        assert data["data"]["service"] == "auth"
        assert data["data"]["status"] == "healthy"
        assert "features" in data["data"]


if __name__ == "__main__":
    pytest.main([__file__, "-v"])
