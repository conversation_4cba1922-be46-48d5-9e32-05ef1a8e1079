from sqlmodel.ext.asyncio.session import AsyncSession
from typing import Union
from app.shared.schemas.response import SuccessResponse, ErrorResponse
from app.modules.auth.schemas.user import UserCreate, UserLogin, UserResponse, EmailVerificationRequest, EmailVerificationResponse
from app.modules.auth.services.auth_service import AuthService
from app.utils.exceptions.base_exceptions import AppBaseException
from app.utils.constants.error_codes import (
    AUTH_EMAIL_EXISTS,
    AUTH_PASSWORD_WEAK,
    AUTH_INVALID_CREDENTIALS,
    AUTH_ACCOUNT_INACTIVE,
    AUTH_VERIFICATION_REQUIRED,
    USER_INVALID_DATA
)

class AuthController:

    def __init__(self, db: AsyncSession):
        self.auth_service = AuthService(db)
    
    async def register_user(self, user_data: UserCreate) -> Union[SuccessResponse[UserResponse], ErrorResponse]:
        try:
            user = await self.auth_service.create_user(user_data)
            
            user_response = UserResponse(
                user_id=user.id,
                email=user.email,
                display_name=user.display_name,
                is_verified=user.is_verified,
                created_at=user.created_at,
                full_name=user.profile.full_name if user.profile else None,
                phone=user.profile.phone if user.profile else None,
                avatar_url=user.profile.avatar_url if user.profile else None
            )

            return SuccessResponse(
                message="Đăng ký thành công! Vui lòng kiểm tra email để xác thực tài khoản.",
                data=user_response
            )

        except AppBaseException as e:
            if e.error_code == AUTH_EMAIL_EXISTS:
                return ErrorResponse(
                    message=e.message,
                    error_code=e.error_code,
                    error_details={"email": user_data.email}
                )
            elif e.error_code == AUTH_PASSWORD_WEAK:
                return ErrorResponse(
                    message=e.message,
                    error_code=e.error_code,
                    error_details={
                        "requirements": [
                            "Ít nhất 8 ký tự",
                            "Có chữ hoa",
                            "Có chữ thường",
                            "Có số",
                            "Có ký tự đặc biệt"
                        ]
                    }
                )
            elif e.error_code == USER_INVALID_DATA:
                return ErrorResponse(
                    message=e.message,
                    error_code=e.error_code,
                    error_details={"validation_error": str(e)}
                )
            else:
                return ErrorResponse(
                    message=e.message,
                    error_code=e.error_code or "UNKNOWN_ERROR"
                )

        except Exception as e:
            print(f"Unexpected error in register_user: {str(e)}")
            return ErrorResponse(
                message="Có lỗi xảy ra, vui lòng thử lại",
                error_code="INTERNAL_ERROR"
            )
    
    async def login_user(self, credentials: UserLogin) -> Union[SuccessResponse[UserResponse], ErrorResponse]:
        try:
            user = await self.auth_service.authenticate_user(
                credentials.email,
                credentials.password
            )

            user_response = UserResponse(
                user_id=user.id,
                email=user.email,
                display_name=user.display_name,
                is_verified=user.is_verified,
                created_at=user.created_at,
                full_name=user.profile.full_name if user.profile else None,
                phone=user.profile.phone if user.profile else None,
                avatar_url=user.profile.avatar_url if user.profile else None
            )

            return SuccessResponse(
                message="Đăng nhập thành công",
                data=user_response
            )

        except AppBaseException as e:
            if e.error_code == AUTH_INVALID_CREDENTIALS:
                return ErrorResponse(
                    message=e.message,
                    error_code=e.error_code,
                    error_details={"attempted_email": credentials.email}
                )
            elif e.error_code == AUTH_ACCOUNT_INACTIVE:
                return ErrorResponse(
                    message=e.message,
                    error_code=e.error_code,
                    error_details={"email": credentials.email}
                )
            elif e.error_code == AUTH_VERIFICATION_REQUIRED:
                return ErrorResponse(
                    message=e.message,
                    error_code=e.error_code,
                    error_details={
                        "email": credentials.email,
                        "verification_required": True
                    }
                )
            else:
                return ErrorResponse(
                    message=e.message,
                    error_code=e.error_code or "UNKNOWN_ERROR"
                )

        except Exception as e:
            print(f"Unexpected error in login_user: {str(e)}")
            return ErrorResponse(
                message="Có lỗi xảy ra, vui lòng thử lại",
                error_code="INTERNAL_ERROR"
            )
    


    async def verify_email(self, verification_data: EmailVerificationRequest) -> Union[SuccessResponse[EmailVerificationResponse], ErrorResponse]:
        try:
            success = await self.auth_service.verify_email(
                verification_data.email,
                verification_data.token
            )
            if success:
                response = EmailVerificationResponse(
                    message="Email đã được xác thực thành công! Bạn có thể đăng nhập ngay bây giờ.",
                    is_verified=True
                )

                return SuccessResponse(
                    message="Xác thực email thành công",
                    data=response
                )
            else:
                return ErrorResponse(
                    message="Xác thực email thất bại",
                    error_code=AUTH_INVALID_CREDENTIALS
                )

        except AppBaseException as e:
            return ErrorResponse(
                message=e.message,
                error_code=e.error_code or AUTH_INVALID_CREDENTIALS,
                error_details={
                    "email": verification_data.email,
                    "verification_failed": True
                }
            )

        except Exception as e:
            return ErrorResponse(
                message="Xác thực email thất bại do lỗi hệ thống",
                error_code="SYSTEM_ERROR",
                error_details={"error": str(e)}
            )

    async def health_check(self) -> SuccessResponse[dict]:
        return SuccessResponse(
            message="Auth service đang hoạt động bình thường",
            data={
                "service": "auth",
                "status": "healthy",
                "features": {
                    "user_registration": True,
                    "user_authentication": True,
                    "jwt_tokens": False,
                    "email_verification": True 
                }
            }
        )
