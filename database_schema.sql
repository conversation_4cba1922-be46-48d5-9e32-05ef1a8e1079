-- =====================================================
-- SkinAid Database Schema - PostgreSQL
-- =====================================================
-- Dự án: C1SE.24_SkinAid_Capstone1
-- Mô tả: Schema database cho hệ thống phát hiện và phân loại vết thương
-- Ngày tạo: 2025-09-16
-- =====================================================

-- =====================================================
-- 1. ENUM TYPES
-- =====================================================

-- Enum cho giới tính
CREATE TYPE gender_enum AS ENUM ('male', 'female', 'other');

-- =====================================================
-- 2. TABLES
-- =====================================================

-- -----------------------------------------------------
-- Table: users
-- Mô tả: Lưu thông tin tài khoản người dùng
-- -----------------------------------------------------
CREATE TABLE users (
    -- Primary Key
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    
    -- Authentication fields
    email VARCHAR(255) NOT NULL UNIQUE,
    hashed_password VARCHAR(255) NOT NULL,
    
    -- Status fields
    is_active BOOLEAN NOT NULL DEFAULT TRUE,
    is_verified BOOLEAN NOT NULL DEFAULT FALSE,
    
    -- Timestamp fields (từ TimestampMixin)
    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW()
);

-- Indexes cho table users
CREATE INDEX idx_users_email ON users(email);
CREATE INDEX idx_users_is_active ON users(is_active);
CREATE INDEX idx_users_is_verified ON users(is_verified);

-- -----------------------------------------------------
-- Table: user_profiles
-- Mô tả: Lưu thông tin profile chi tiết của người dùng
-- Quan hệ: 1-1 với users
-- -----------------------------------------------------
CREATE TABLE user_profiles (
    -- Primary Key
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    
    -- Foreign Key to users (1-1 relationship)
    user_id UUID NOT NULL UNIQUE REFERENCES users(id) ON DELETE CASCADE,
    
    -- Profile information fields
    full_name VARCHAR(255) DEFAULT NULL,
    phone VARCHAR(20) DEFAULT NULL,
    date_of_birth DATE DEFAULT NULL,
    gender gender_enum DEFAULT NULL,
    address VARCHAR(255) DEFAULT NULL,
    avatar_url VARCHAR(500) DEFAULT NULL,
    
    -- Timestamp fields (từ TimestampMixin)
    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW()
);

-- Indexes cho table user_profiles
CREATE INDEX idx_user_profiles_user_id ON user_profiles(user_id);
CREATE INDEX idx_user_profiles_full_name ON user_profiles(full_name);
CREATE INDEX idx_user_profiles_phone ON user_profiles(phone);

-- -----------------------------------------------------
-- Table: verification_tokens
-- Mô tả: Lưu token xác thực email của người dùng
-- Quan hệ: N-1 với users (một user có thể có nhiều token)
-- -----------------------------------------------------
CREATE TABLE verification_tokens (
    -- Primary Key
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    
    -- Foreign Key to users (N-1 relationship)
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    
    -- Token information
    email VARCHAR(255) NOT NULL,
    token VARCHAR(255) NOT NULL UNIQUE,
    
    -- Token status and expiration
    expires_at TIMESTAMP WITH TIME ZONE NOT NULL,
    is_used BOOLEAN NOT NULL DEFAULT FALSE,
    
    -- Timestamp fields (từ TimestampMixin)
    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW()
);

-- Indexes cho table verification_tokens
CREATE INDEX idx_verification_tokens_user_id ON verification_tokens(user_id);
CREATE INDEX idx_verification_tokens_email ON verification_tokens(email);
CREATE INDEX idx_verification_tokens_token ON verification_tokens(token);
CREATE INDEX idx_verification_tokens_expires_at ON verification_tokens(expires_at);
CREATE INDEX idx_verification_tokens_is_used ON verification_tokens(is_used);

-- =====================================================
-- 3. CONSTRAINTS & RELATIONSHIPS
-- =====================================================

-- Constraint: Email format validation
ALTER TABLE users ADD CONSTRAINT chk_users_email_format 
    CHECK (email ~* '^[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Za-z]{2,}$');

-- Constraint: Phone format validation (optional)
ALTER TABLE user_profiles ADD CONSTRAINT chk_user_profiles_phone_format 
    CHECK (phone IS NULL OR phone ~* '^[0-9+\-\s()]{10,20}$');

-- Constraint: Date of birth không được trong tương lai
ALTER TABLE user_profiles ADD CONSTRAINT chk_user_profiles_dob_past 
    CHECK (date_of_birth IS NULL OR date_of_birth <= CURRENT_DATE);

-- Constraint: Token expiration phải trong tương lai khi tạo
ALTER TABLE verification_tokens ADD CONSTRAINT chk_verification_tokens_expires_future 
    CHECK (expires_at > created_at);

-- =====================================================
-- 4. TRIGGERS FOR AUTO-UPDATE TIMESTAMPS
-- =====================================================

-- Function để tự động update updated_at
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Trigger cho table users
CREATE TRIGGER update_users_updated_at 
    BEFORE UPDATE ON users 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Trigger cho table user_profiles
CREATE TRIGGER update_user_profiles_updated_at 
    BEFORE UPDATE ON user_profiles 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Trigger cho table verification_tokens
CREATE TRIGGER update_verification_tokens_updated_at 
    BEFORE UPDATE ON verification_tokens 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- =====================================================
-- 5. SAMPLE DATA (Optional - for development)
-- =====================================================

-- Uncomment below to insert sample data for development
/*
-- Sample user
INSERT INTO users (id, email, hashed_password, is_active, is_verified) 
VALUES (
    gen_random_uuid(),
    '<EMAIL>',
    '$argon2id$v=19$m=65536,t=3,p=4$sample_hash',
    TRUE,
    TRUE
);

-- Sample user profile
INSERT INTO user_profiles (user_id, full_name, phone, gender, address)
SELECT 
    u.id,
    'Admin User',
    '0123456789',
    'other',
    '123 Admin Street, Ho Chi Minh City'
FROM users u WHERE u.email = '<EMAIL>';
*/

-- =====================================================
-- 6. USEFUL QUERIES
-- =====================================================

-- Query để lấy user với profile (như trong AuthService)
/*
SELECT
    u.*,
    p.id as profile_id,
    p.full_name,
    p.phone,
    p.date_of_birth,
    p.gender,
    p.address,
    p.avatar_url,
    p.created_at as profile_created_at,
    p.updated_at as profile_updated_at
FROM users u
LEFT JOIN user_profiles p ON u.id = p.user_id
WHERE u.id = $1;
*/

-- Query để kiểm tra token hợp lệ
/*
SELECT vt.*, u.email as user_email
FROM verification_tokens vt
JOIN users u ON vt.user_id = u.id
WHERE vt.token = $1 
  AND vt.is_used = FALSE 
  AND vt.expires_at > NOW();
*/

-- =====================================================
-- END OF SCHEMA
-- =====================================================
