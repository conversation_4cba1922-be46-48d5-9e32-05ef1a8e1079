# 🗄️ SkinAid Database Setup Guide

## 📋 Tổng quan
Dự án SkinAid sử dụng PostgreSQL làm database chính. Hiện tại có 3 bảng cơ bản cho hệ thống authentication và user management.

## 🚀 Cách thực thi

### Option 1: Script đầy đủ (Khuyến nghị)
```bash
psql -U your_username -d your_database -f create_tables.sql
```

### Option 2: Script đơn giản
```bash
psql -U your_username -d your_database -f create_tables_simple.sql
```

## 📊 Cấu trúc Database

### 1. **users** - Bảng tài khoản người dùng
| Field | Type | Constraints | Mô tả |
|-------|------|-------------|-------|
| id | UUID | PRIMARY KEY | ID duy nhất của user |
| email | VARCHAR(255) | NOT NULL, UNIQUE | Email đăng nhập |
| hashed_password | VARCHAR(255) | NOT NULL | Mật khẩu đã hash |
| is_active | BOOLEAN | DEFAULT TRUE | Trạng thái hoạt động |
| is_verified | BOOLEAN | DEFAULT FALSE | Trạng thái xác thực email |
| created_at | TIMESTAMP WITH TIME ZONE | NOT NULL | Thời gian tạo |
| updated_at | TIMESTAMP WITH TIME ZONE | NOT NULL | Thời gian cập nhật |

### 2. **user_profiles** - Bảng thông tin chi tiết người dùng
| Field | Type | Constraints | Mô tả |
|-------|------|-------------|-------|
| id | UUID | PRIMARY KEY | ID duy nhất của profile |
| user_id | UUID | FK → users.id, UNIQUE | Liên kết với user |
| full_name | VARCHAR(255) | NULL | Họ và tên |
| phone | VARCHAR(20) | NULL | Số điện thoại |
| date_of_birth | DATE | NULL | Ngày sinh |
| gender | gender_enum | NULL | Giới tính (male/female/other) |
| address | VARCHAR(255) | NULL | Địa chỉ |
| avatar_url | VARCHAR(500) | NULL | URL ảnh đại diện |
| created_at | TIMESTAMP WITH TIME ZONE | NOT NULL | Thời gian tạo |
| updated_at | TIMESTAMP WITH TIME ZONE | NOT NULL | Thời gian cập nhật |

### 3. **verification_tokens** - Bảng token xác thực email
| Field | Type | Constraints | Mô tả |
|-------|------|-------------|-------|
| id | UUID | PRIMARY KEY | ID duy nhất của token |
| user_id | UUID | FK → users.id | Liên kết với user |
| email | VARCHAR(255) | NOT NULL | Email cần xác thực |
| token | VARCHAR(255) | NOT NULL, UNIQUE | Token xác thực |
| expires_at | TIMESTAMP WITH TIME ZONE | NOT NULL | Thời gian hết hạn |
| is_used | BOOLEAN | DEFAULT FALSE | Trạng thái đã sử dụng |
| created_at | TIMESTAMP WITH TIME ZONE | NOT NULL | Thời gian tạo |
| updated_at | TIMESTAMP WITH TIME ZONE | NOT NULL | Thời gian cập nhật |

## 🔗 Quan hệ giữa các bảng

```
users (1) ←→ (1) user_profiles
users (1) ←→ (N) verification_tokens
```

- **users ↔ user_profiles**: Quan hệ 1-1 (một user có một profile)
- **users ↔ verification_tokens**: Quan hệ 1-N (một user có thể có nhiều token)

## 📝 Lưu ý quan trọng

### 1. **UUID Primary Keys**
- Tất cả bảng sử dụng UUID làm primary key
- Tự động generate bằng `gen_random_uuid()`

### 2. **Timestamps**
- Tất cả bảng có `created_at` và `updated_at`
- Sử dụng `TIMESTAMP WITH TIME ZONE` để hỗ trợ multiple timezone

### 3. **Cascade Delete**
- Khi xóa user → tự động xóa profile và tokens liên quan
- Sử dụng `ON DELETE CASCADE`

### 4. **Enum Type**
- `gender_enum`: 'male', 'female', 'other'

## 🔧 Các tính năng bổ sung (trong script đầy đủ)

### 1. **Indexes**
- Tối ưu performance cho các truy vấn thường dùng
- Index trên email, user_id, token, etc.

### 2. **Constraints**
- Validate email format
- Validate phone format
- Validate date_of_birth không trong tương lai
- Validate token expiration

### 3. **Auto-update Triggers**
- Tự động update `updated_at` khi có thay đổi
- Sử dụng PostgreSQL trigger function

## 🧪 Test Database

### Tạo sample data để test:
```sql
-- Tạo user test
INSERT INTO users (email, hashed_password, is_verified) 
VALUES ('<EMAIL>', '$argon2id$v=19$m=65536,t=3,p=4$sample', true);

-- Tạo profile cho user test
INSERT INTO user_profiles (user_id, full_name, phone, gender)
SELECT id, 'Test User', '0123456789', 'male'
FROM users WHERE email = '<EMAIL>';
```

## 📞 Liên hệ
Nếu có vấn đề gì trong quá trình setup, vui lòng liên hệ team backend để được hỗ trợ.

---
**Dự án**: C1SE.24_SkinAid_Capstone1  
**Database**: PostgreSQL  
**Ngày tạo**: 2025-09-16
