"""
Pytest configuration and shared fixtures
"""

import pytest
import pytest_asyncio
import asyncio
from unittest.mock import AsyncMock
import uuid
from datetime import datetime, timezone

from app.modules.auth.models.user import User
from app.modules.profile.models.user_profile import UserProfile
from app.modules.auth.schemas.user import UserCreate, UserLogin
from app.modules.profile.schemas.user_profile import UserProfileUpdate, GenderEnum


@pytest.fixture(scope="session")
def event_loop():
    """Create an instance of the default event loop for the test session."""
    loop = asyncio.get_event_loop_policy().new_event_loop()
    yield loop
    loop.close()


@pytest.fixture
def mock_db_session():
    """Mock database session for testing"""
    session = AsyncMock()
    session.execute = AsyncMock()
    session.commit = AsyncMock()
    session.rollback = AsyncMock()
    session.close = AsyncMock()
    return session


@pytest.fixture
def sample_user_id():
    """Sample user UUID"""
    return uuid.uuid4()


@pytest.fixture
def sample_user_create():
    """Sample UserCreate data for testing"""
    return UserCreate(
        email="<EMAIL>",
        password="TestPassword123!",
        confirm_password="TestPassword123!"
    )


@pytest.fixture
def sample_user_login():
    """Sample UserLogin data for testing"""
    return UserLogin(
        email="<EMAIL>",
        password="TestPassword123!"
    )


@pytest.fixture
def sample_user(sample_user_id):
    """Sample User model for testing"""
    return User(
        id=sample_user_id,
        email="<EMAIL>",
        hashed_password="$argon2id$v=19$m=65536,t=3,p=4$test_hash",
        is_active=True,
        is_verified=False,
        created_at=datetime.now(timezone.utc),
        updated_at=datetime.now(timezone.utc)
    )


@pytest.fixture
def sample_verified_user(sample_user_id):
    """Sample verified User model for testing"""
    return User(
        id=sample_user_id,
        email="<EMAIL>",
        hashed_password="$argon2id$v=19$m=65536,t=3,p=4$test_hash",
        is_active=True,
        is_verified=True,
        created_at=datetime.now(timezone.utc),
        updated_at=datetime.now(timezone.utc)
    )


@pytest.fixture
def sample_user_profile(sample_user_id):
    """Sample UserProfile model for testing"""
    return UserProfile(
        id=uuid.uuid4(),
        user_id=sample_user_id,
        full_name="Test User",
        phone="0123456789",
        date_of_birth=datetime(1990, 1, 1).date(),
        gender=GenderEnum.male,
        address="123 Test Street",
        avatar_url="https://example.com/avatar.jpg",
        created_at=datetime.now(timezone.utc),
        updated_at=datetime.now(timezone.utc)
    )


@pytest.fixture
def sample_profile_update():
    """Sample UserProfileUpdate data for testing"""
    return UserProfileUpdate(
        full_name="Updated Test User",
        phone="0987654321",
        address="456 Updated Street"
    )


@pytest.fixture
def mock_email_service():
    """Mock email service for testing"""
    service = AsyncMock()
    service.send_verification_email = AsyncMock(return_value=True)
    service.send_welcome_email = AsyncMock(return_value=True)
    service.generate_verification_token = AsyncMock(return_value="test_token_123")
    return service


@pytest.fixture
def mock_password_context():
    """Mock password context for testing"""
    context = AsyncMock()
    context.hash = AsyncMock(return_value="hashed_password")
    context.verify = AsyncMock(return_value=True)
    return context


# Test data constants
TEST_EMAIL = "<EMAIL>"
TEST_PASSWORD = "TestPassword123!"
TEST_HASHED_PASSWORD = "$argon2id$v=19$m=65536,t=3,p=4$test_hash"
TEST_TOKEN = "test_verification_token_123"


@pytest.fixture
def test_constants():
    """Test constants for consistent test data"""
    return {
        "email": TEST_EMAIL,
        "password": TEST_PASSWORD,
        "hashed_password": TEST_HASHED_PASSWORD,
        "token": TEST_TOKEN
    }


# Async test helpers
@pytest.fixture
def async_mock():
    """Helper to create async mocks"""
    def _async_mock(*args, **kwargs):
        mock = AsyncMock(*args, **kwargs)
        return mock
    return _async_mock


# Database test helpers
@pytest.fixture
def mock_db_result():
    """Mock database result for testing"""
    def _mock_result(data=None, multiple=False):
        result = AsyncMock()
        mappings = AsyncMock()
        
        if multiple:
            mappings.all.return_value = data or []
            mappings.first.return_value = data[0] if data else None
        else:
            mappings.first.return_value = data
            mappings.all.return_value = [data] if data else []
        
        result.mappings.return_value = mappings
        result.scalar.return_value = data if not isinstance(data, (list, dict)) else None
        result.fetchall.return_value = data if isinstance(data, list) else [data] if data else []
        
        return result
    
    return _mock_result


# Error testing helpers
@pytest.fixture
def validation_error_cases():
    """Common validation error test cases"""
    return {
        "user_create": [
            {
                "name": "missing_email",
                "data": {"password": "Test123!", "confirm_password": "Test123!"},
                "should_fail": True
            },
            {
                "name": "invalid_email",
                "data": {"email": "not-an-email", "password": "Test123!", "confirm_password": "Test123!"},
                "should_fail": True
            },
            {
                "name": "password_mismatch",
                "data": {"email": "<EMAIL>", "password": "Test123!", "confirm_password": "Different123!"},
                "should_fail": True
            },
            {
                "name": "valid_data",
                "data": {"email": "<EMAIL>", "password": "Test123!", "confirm_password": "Test123!"},
                "should_fail": False
            }
        ],
        "profile_update": [
            {
                "name": "invalid_gender",
                "data": {"gender": "invalid_gender"},
                "should_fail": True
            },
            {
                "name": "valid_gender",
                "data": {"gender": GenderEnum.male},
                "should_fail": False
            },
            {
                "name": "empty_profile",
                "data": {},
                "should_fail": False
            }
        ]
    }


# Performance testing helpers
@pytest.fixture
def performance_timer():
    """Timer for performance testing"""
    import time
    
    class Timer:
        def __init__(self):
            self.start_time = None
            self.end_time = None
        
        def start(self):
            self.start_time = time.time()
        
        def stop(self):
            self.end_time = time.time()
        
        @property
        def elapsed(self):
            if self.start_time and self.end_time:
                return self.end_time - self.start_time
            return None
    
    return Timer()


# Cleanup helpers
@pytest.fixture(autouse=True)
def cleanup_test_data():
    """Automatically cleanup test data after each test"""
    yield
    # Cleanup code would go here if needed
    # For now, we're using mocks so no real cleanup needed
