-- =====================================================
-- SkinAid Database - CREATE TABLES Script
-- =====================================================
-- Dự án: C1SE.24_SkinAid_Capstone1
-- Mô tả: Script tạo các bảng cho hệ thống SkinAid
-- Database: PostgreSQL
-- Ngày tạo: 2025-09-16
-- =====================================================

-- Tạo ENUM type cho giới tính
CREATE TYPE gender_enum AS ENUM ('male', 'female', 'other');

-- =====================================================
-- Tạo bảng users
-- =====================================================
CREATE TABLE users (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    email VARCHAR(255) NOT NULL UNIQUE,
    hashed_password VARCHAR(255) NOT NULL,
    is_active BOOLEAN NOT NULL DEFAULT TRUE,
    is_verified BOOLEAN NOT NULL DEFAULT FALSE,
    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW()
);

-- =====================================================
-- Tạo bảng user_profiles
-- =====================================================
CREATE TABLE user_profiles (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL UNIQUE REFERENCES users(id) ON DELETE CASCADE,
    full_name VARCHAR(255) DEFAULT NULL,
    phone VARCHAR(20) DEFAULT NULL,
    date_of_birth DATE DEFAULT NULL,
    gender gender_enum DEFAULT NULL,
    address VARCHAR(255) DEFAULT NULL,
    avatar_url VARCHAR(500) DEFAULT NULL,
    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW()
);

-- =====================================================
-- Tạo bảng verification_tokens
-- =====================================================
CREATE TABLE verification_tokens (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    email VARCHAR(255) NOT NULL,
    token VARCHAR(255) NOT NULL UNIQUE,
    expires_at TIMESTAMP WITH TIME ZONE NOT NULL,
    is_used BOOLEAN NOT NULL DEFAULT FALSE,
    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW()
);

-- =====================================================
-- Tạo các INDEX để tối ưu performance
-- =====================================================

-- Indexes cho bảng users
CREATE INDEX idx_users_email ON users(email);
CREATE INDEX idx_users_is_active ON users(is_active);
CREATE INDEX idx_users_is_verified ON users(is_verified);

-- Indexes cho bảng user_profiles
CREATE INDEX idx_user_profiles_user_id ON user_profiles(user_id);
CREATE INDEX idx_user_profiles_full_name ON user_profiles(full_name);
CREATE INDEX idx_user_profiles_phone ON user_profiles(phone);

-- Indexes cho bảng verification_tokens
CREATE INDEX idx_verification_tokens_user_id ON verification_tokens(user_id);
CREATE INDEX idx_verification_tokens_email ON verification_tokens(email);
CREATE INDEX idx_verification_tokens_token ON verification_tokens(token);
CREATE INDEX idx_verification_tokens_expires_at ON verification_tokens(expires_at);
CREATE INDEX idx_verification_tokens_is_used ON verification_tokens(is_used);

-- =====================================================
-- Tạo CONSTRAINTS để validate dữ liệu
-- =====================================================

-- Validate email format
ALTER TABLE users ADD CONSTRAINT chk_users_email_format 
    CHECK (email ~* '^[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Za-z]{2,}$');

-- Validate phone format (optional)
ALTER TABLE user_profiles ADD CONSTRAINT chk_user_profiles_phone_format 
    CHECK (phone IS NULL OR phone ~* '^[0-9+\-\s()]{10,20}$');

-- Validate date of birth không được trong tương lai
ALTER TABLE user_profiles ADD CONSTRAINT chk_user_profiles_dob_past 
    CHECK (date_of_birth IS NULL OR date_of_birth <= CURRENT_DATE);

-- Validate token expiration phải trong tương lai khi tạo
ALTER TABLE verification_tokens ADD CONSTRAINT chk_verification_tokens_expires_future 
    CHECK (expires_at > created_at);

-- =====================================================
-- Tạo FUNCTION và TRIGGER để tự động update updated_at
-- =====================================================

-- Function để update updated_at column
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Trigger cho bảng users
CREATE TRIGGER update_users_updated_at 
    BEFORE UPDATE ON users 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Trigger cho bảng user_profiles
CREATE TRIGGER update_user_profiles_updated_at 
    BEFORE UPDATE ON user_profiles 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Trigger cho bảng verification_tokens
CREATE TRIGGER update_verification_tokens_updated_at 
    BEFORE UPDATE ON verification_tokens 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- =====================================================
-- Script hoàn thành!
-- =====================================================
-- Các bảng đã được tạo:
-- 1. users - Lưu thông tin tài khoản
-- 2. user_profiles - Lưu thông tin profile chi tiết
-- 3. verification_tokens - Lưu token xác thực email
-- 
-- Các tính năng đã được thêm:
-- - UUID primary keys
-- - Foreign key constraints
-- - Indexes cho performance
-- - Data validation constraints
-- - Auto-update timestamps
-- =====================================================
