"""
Unit tests for AuthService
"""

import pytest
import pytest_asyncio
from unittest.mock import AsyncMock, patch
import uuid
from datetime import datetime, timezone

from app.modules.auth.services.auth_service import AuthService
from app.modules.auth.schemas.user import UserCreate, UserLogin
from app.modules.auth.models.user import User
from app.utils.exceptions.base_exceptions import AppBaseException
from app.utils.constants.error_codes import AUTH_EMAIL_EXISTS, AUTH_INVALID_CREDENTIALS, AUTH_VERIFICATION_REQUIRED


@pytest.fixture
def mock_db_session():
    """Mock database session"""
    return AsyncMock()


@pytest.fixture
def auth_service(mock_db_session):
    """Create AuthService instance with mocked database"""
    return AuthService(mock_db_session)


@pytest.fixture
def sample_user_create():
    """Sample UserCreate data"""
    return UserCreate(
        email="<EMAIL>",
        password="TestPassword123!",
        confirm_password="TestPassword123!"
    )


@pytest.fixture
def sample_user():
    """Sample User model"""
    return User(
        id=uuid.uuid4(),
        email="<EMAIL>",
        hashed_password="$argon2id$v=19$m=65536,t=3,p=4$test",
        is_active=True,
        is_verified=False,
        created_at=datetime.now(timezone.utc),
        updated_at=datetime.now(timezone.utc)
    )


class TestAuthService:
    """Test cases for AuthService"""
    
    @pytest.mark.asyncio
    async def test_create_user_success(self, auth_service, sample_user_create, mock_db_session):
        """Test successful user creation"""
        # Mock get_user_by_email to return None (no existing user)
        from unittest.mock import MagicMock
        mock_scalars_result = MagicMock()
        mock_scalars_result.first.return_value = None

        mock_execute_result = AsyncMock()
        mock_execute_result.scalars.return_value = mock_scalars_result

        # Mock successful user creation
        created_user_data = {
            "id": uuid.uuid4(),
            "email": sample_user_create.email,
            "hashed_password": "hashed_password",
            "is_active": True,
            "is_verified": False,
            "created_at": datetime.now(timezone.utc),
            "updated_at": datetime.now(timezone.utc)
        }

        # Mock mappings for SQL queries
        mock_mappings_result = AsyncMock()
        mock_mappings_result.mappings.return_value.first.side_effect = [
            created_user_data,  # Created user
            {"id": uuid.uuid4(), "user_id": created_user_data["id"]},  # Created profile
            {"id": uuid.uuid4(), "user_id": created_user_data["id"], "token": "test_token"}  # Created token
        ]

        # Configure mock to return different results for different calls
        mock_db_session.execute.side_effect = [
            mock_execute_result,  # get_user_by_email call
            mock_mappings_result,  # user creation
            mock_mappings_result,  # profile creation
            mock_mappings_result   # token creation
        ]

        with patch('app.utils.email_service.email_service.send_verification_email', return_value=True):
            result = await auth_service.create_user(sample_user_create)

            assert result is not None
            assert result.email == sample_user_create.email
            assert result.is_verified == False
    
    @pytest.mark.asyncio
    async def test_create_user_email_exists(self, auth_service, sample_user_create, mock_db_session):
        """Test user creation with existing email"""
        # Mock existing user found by get_user_by_email
        existing_user = User(
            id=uuid.uuid4(),
            email=sample_user_create.email,
            hashed_password="existing_hash",
            is_active=True,
            is_verified=True,
            created_at=datetime.now(timezone.utc),
            updated_at=datetime.now(timezone.utc)
        )

        from unittest.mock import MagicMock
        mock_scalars_result = MagicMock()
        mock_scalars_result.first.return_value = existing_user

        mock_execute_result = AsyncMock()
        mock_execute_result.scalars.return_value = mock_scalars_result
        mock_db_session.execute.return_value = mock_execute_result
        
        with pytest.raises(AppBaseException) as exc_info:
            await auth_service.create_user(sample_user_create)
        
        assert exc_info.value.error_code == AUTH_EMAIL_EXISTS
    
    @pytest.mark.asyncio
    async def test_authenticate_user_success(self, auth_service, mock_db_session):
        """Test successful user authentication"""
        # Mock verified user for get_user_by_email
        user = User(
            id=uuid.uuid4(),
            email="<EMAIL>",
            hashed_password="$argon2id$v=19$m=65536,t=3,p=4$test",
            is_active=True,
            is_verified=True,
            created_at=datetime.now(timezone.utc),
            updated_at=datetime.now(timezone.utc)
        )

        # Mock get_user_by_email result
        from unittest.mock import MagicMock
        mock_scalars_result = MagicMock()
        mock_scalars_result.first.return_value = user
        mock_execute_result = AsyncMock()
        mock_execute_result.scalars.return_value = mock_scalars_result

        # Mock get_user_by_id result (for final return)
        user_with_profile_data = {
            "id": user.id,
            "email": user.email,
            "hashed_password": user.hashed_password,
            "is_active": user.is_active,
            "is_verified": user.is_verified,
            "created_at": user.created_at,
            "updated_at": user.updated_at,
            "profile_id": None,
            "full_name": None,
            "phone": None,
            "date_of_birth": None,
            "gender": None,
            "address": None,
            "avatar_url": None,
            "profile_created_at": None,
            "profile_updated_at": None
        }
        mock_mappings_result = AsyncMock()
        mock_mappings_result.mappings.return_value.first.return_value = user_with_profile_data

        # Configure mock to return different results for different calls
        mock_db_session.execute.side_effect = [
            mock_execute_result,  # get_user_by_email call
            mock_mappings_result,  # update query
            mock_mappings_result   # get_user_by_id call
        ]

        with patch('app.core.security.verify_password', return_value=True):
            result = await auth_service.authenticate_user("<EMAIL>", "correct_password")

            assert result is not None
            assert result.email == "<EMAIL>"
    
    @pytest.mark.asyncio
    async def test_authenticate_user_not_verified(self, auth_service, mock_db_session):
        """Test authentication with unverified user"""
        # Mock unverified user for get_user_by_email
        user = User(
            id=uuid.uuid4(),
            email="<EMAIL>",
            hashed_password="$argon2id$v=19$m=65536,t=3,p=4$test",
            is_active=True,
            is_verified=False,  # Not verified
            created_at=datetime.now(timezone.utc),
            updated_at=datetime.now(timezone.utc)
        )

        from unittest.mock import MagicMock
        mock_scalars_result = MagicMock()
        mock_scalars_result.first.return_value = user
        mock_execute_result = AsyncMock()
        mock_execute_result.scalars.return_value = mock_scalars_result
        mock_db_session.execute.return_value = mock_execute_result

        with pytest.raises(AppBaseException) as exc_info:
            await auth_service.authenticate_user("<EMAIL>", "password")

        assert exc_info.value.error_code == AUTH_VERIFICATION_REQUIRED
    
    @pytest.mark.asyncio
    async def test_authenticate_user_invalid_credentials(self, auth_service, mock_db_session):
        """Test authentication with invalid credentials"""
        # Mock no user found
        from unittest.mock import MagicMock
        mock_scalars_result = MagicMock()
        mock_scalars_result.first.return_value = None
        mock_execute_result = AsyncMock()
        mock_execute_result.scalars.return_value = mock_scalars_result
        mock_db_session.execute.return_value = mock_execute_result

        with pytest.raises(AppBaseException) as exc_info:
            await auth_service.authenticate_user("<EMAIL>", "password")

        assert exc_info.value.error_code == AUTH_INVALID_CREDENTIALS

    @pytest.mark.asyncio
    async def test_get_user_by_email_success(self, auth_service, mock_db_session):
        """Test getting user by email"""
        user = User(
            id=uuid.uuid4(),
            email="<EMAIL>",
            hashed_password="hash",
            is_active=True,
            is_verified=True,
            created_at=datetime.now(timezone.utc),
            updated_at=datetime.now(timezone.utc)
        )

        from unittest.mock import MagicMock
        mock_scalars_result = MagicMock()
        mock_scalars_result.first.return_value = user
        mock_execute_result = AsyncMock()
        mock_execute_result.scalars.return_value = mock_scalars_result
        mock_db_session.execute.return_value = mock_execute_result

        result = await auth_service.get_user_by_email("<EMAIL>")

        assert result is not None
        assert result.email == "<EMAIL>"

    @pytest.mark.asyncio
    async def test_get_user_by_email_not_found(self, auth_service, mock_db_session):
        """Test getting non-existent user by email"""
        from unittest.mock import MagicMock
        mock_scalars_result = MagicMock()
        mock_scalars_result.first.return_value = None
        mock_execute_result = AsyncMock()
        mock_execute_result.scalars.return_value = mock_scalars_result
        mock_db_session.execute.return_value = mock_execute_result
        
        result = await auth_service.get_user_by_email("<EMAIL>")
        
        assert result is None


class TestAuthServiceValidation:
    """Test validation in AuthService"""
    
    @pytest.mark.asyncio
    async def test_password_hashing(self, auth_service):
        """Test password hashing functionality"""
        password = "TestPassword123!"
        
        with patch('passlib.context.CryptContext.hash') as mock_hash:
            mock_hash.return_value = "hashed_password"
            
            # This would be called internally in create_user
            # We're testing the concept here
            hashed = mock_hash(password)
            
            assert hashed == "hashed_password"
            mock_hash.assert_called_once_with(password)
    
    @pytest.mark.asyncio
    async def test_password_verification(self, auth_service):
        """Test password verification functionality"""
        password = "TestPassword123!"
        hashed_password = "$argon2id$v=19$m=65536,t=3,p=4$test"
        
        with patch('passlib.context.CryptContext.verify') as mock_verify:
            mock_verify.return_value = True
            
            # This would be called internally in authenticate_user
            is_valid = mock_verify(password, hashed_password)
            
            assert is_valid == True
            mock_verify.assert_called_once_with(password, hashed_password)


if __name__ == "__main__":
    pytest.main([__file__])
