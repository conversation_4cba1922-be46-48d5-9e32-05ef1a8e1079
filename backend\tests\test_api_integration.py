"""
Integration tests for API endpoints
Tests the complete API flow from HTTP request to response
"""

import pytest
import pytest_asyncio
from httpx import AsyncClient
from fastapi.testclient import TestClient
import asyncio
from unittest.mock import patch, AsyncMock
import uuid
from datetime import datetime, timezone

from app.main import app
from app.core.database import get_db
from app.modules.auth.schemas.user import User<PERSON>reate, User<PERSON>ogin, EmailVerificationRequest
from app.modules.profile.schemas.user_profile import UserProfileUpdate, GenderEnum


# Test client setup
@pytest.fixture
def client():
    """Create test client"""
    return TestClient(app)


@pytest.fixture
async def async_client():
    """Create async test client"""
    async with Async<PERSON>lient(app=app, base_url="http://test") as ac:
        yield ac


@pytest.fixture
def mock_db():
    """Mock database dependency"""
    mock_db = AsyncMock()
    return mock_db


@pytest.fixture
def override_get_db(mock_db):
    """Override database dependency"""
    def _override_get_db():
        return mock_db
    
    app.dependency_overrides[get_db] = _override_get_db
    yield
    app.dependency_overrides.clear()


class TestRootEndpoint:
    """Test root endpoint"""
    
    def test_root_endpoint(self, client):
        """Test GET / endpoint"""
        response = client.get("/")
        
        assert response.status_code == 200
        data = response.json()
        assert "app" in data
        assert "version" in data
        assert "message" in data
        assert data["message"] == "API is running"


class TestAuthEndpoints:
    """Test authentication endpoints"""
    
    def test_auth_health_check(self, client, override_get_db):
        """Test GET /api/v1/auth/health endpoint"""
        response = client.get("/api/v1/auth/health")
        
        assert response.status_code == 200
        data = response.json()
        assert data["success"] is True
        assert "data" in data
        assert data["data"]["service"] == "auth"
        assert data["data"]["status"] == "healthy"
    
    def test_register_endpoint_structure(self, client, override_get_db, mock_db):
        """Test POST /api/v1/auth/register endpoint structure"""
        # Mock successful registration
        with patch('app.modules.auth.services.auth_service.AuthService.create_user') as mock_create:
            # Mock user creation with proper attributes
            from unittest.mock import MagicMock
            mock_user = MagicMock()
            mock_user.id = uuid.uuid4()
            mock_user.email = "<EMAIL>"
            mock_user.display_name = "test"
            mock_user.is_verified = False
            mock_user.created_at = datetime.now(timezone.utc)
            mock_user.profile = None
            mock_create.return_value = mock_user
            
            user_data = {
                "email": "<EMAIL>",
                "password": "TestPassword123!",
                "confirm_password": "TestPassword123!"
            }
            
            response = client.post("/api/v1/auth/register", json=user_data)
            
            # Should return 201 for successful registration
            assert response.status_code == 201
            data = response.json()
            assert data["success"] is True
            assert "data" in data
    
    def test_register_validation_error(self, client, override_get_db):
        """Test registration with validation errors"""
        # Test with invalid email
        user_data = {
            "email": "invalid-email",
            "password": "TestPassword123!",
            "confirm_password": "TestPassword123!"
        }
        
        response = client.post("/api/v1/auth/register", json=user_data)
        
        # Should return 422 for validation error
        assert response.status_code == 422
    
    def test_register_password_mismatch(self, client, override_get_db):
        """Test registration with password mismatch"""
        user_data = {
            "email": "<EMAIL>",
            "password": "TestPassword123!",
            "confirm_password": "DifferentPassword123!"
        }
        
        response = client.post("/api/v1/auth/register", json=user_data)
        
        # Should return 422 for validation error
        assert response.status_code == 422
    
    def test_login_endpoint_structure(self, client, override_get_db):
        """Test POST /api/v1/auth/login endpoint structure"""
        with patch('app.modules.auth.services.auth_service.AuthService.authenticate_user') as mock_auth:
            # Mock successful authentication
            from unittest.mock import MagicMock
            mock_user = MagicMock()
            mock_user.id = uuid.uuid4()
            mock_user.email = "<EMAIL>"
            mock_user.display_name = "test"
            mock_user.is_verified = True
            mock_user.created_at = datetime.now(timezone.utc)
            mock_user.profile = None
            mock_auth.return_value = mock_user
            
            login_data = {
                "email": "<EMAIL>",
                "password": "TestPassword123!"
            }
            
            response = client.post("/api/v1/auth/login", json=login_data)
            
            assert response.status_code == 200
            data = response.json()
            assert data["success"] is True
            assert "data" in data
    
    def test_verify_email_endpoint_structure(self, client, override_get_db):
        """Test POST /api/v1/auth/verify-email endpoint structure"""
        with patch('app.modules.auth.services.auth_service.AuthService.verify_email') as mock_verify:
            mock_verify.return_value = True
            
            verify_data = {
                "email": "<EMAIL>",
                "token": "test_token_123"
            }
            
            response = client.post("/api/v1/auth/verify-email", json=verify_data)
            
            assert response.status_code == 200
            data = response.json()
            assert data["success"] is True
            assert "data" in data


class TestProfileEndpoints:
    """Test profile endpoints"""
    
    def test_get_profile_endpoint_structure(self, client, override_get_db):
        """Test GET /api/v1/profile/me endpoint structure"""
        with patch('app.modules.profile.services.profile_service.ProfileService.get_profile_by_user_id') as mock_get:
            with patch('app.modules.profile.services.profile_service.ProfileService.create_profile_response') as mock_response:
                # Mock profile data
                mock_profile = AsyncMock()
                mock_get.return_value = mock_profile
                
                mock_profile_response = {
                    "user_id": str(uuid.uuid4()),
                    "full_name": "Test User",
                    "phone": "1234567890",
                    "date_of_birth": None,
                    "gender": None,
                    "address": None,
                    "avatar_url": None,
                    "created_at": datetime.now(timezone.utc),
                    "updated_at": datetime.now(timezone.utc)
                }
                mock_response.return_value = mock_profile_response
                
                response = client.get("/api/v1/profile/me")
                
                assert response.status_code == 200
                data = response.json()
                assert data["success"] is True
                assert "data" in data
    
    def test_update_profile_endpoint_structure(self, client, override_get_db):
        """Test PUT /api/v1/profile/update endpoint structure"""
        with patch('app.modules.profile.services.profile_service.ProfileService.update_profile') as mock_update:
            with patch('app.modules.profile.services.profile_service.ProfileService.create_profile_response') as mock_response:
                # Mock updated profile
                mock_profile = AsyncMock()
                mock_update.return_value = mock_profile
                
                mock_profile_response = {
                    "user_id": str(uuid.uuid4()),
                    "full_name": "Updated User",
                    "phone": "0987654321",
                    "date_of_birth": None,
                    "gender": "male",
                    "address": "123 Test St",
                    "avatar_url": None,
                    "created_at": datetime.now(timezone.utc),
                    "updated_at": datetime.now(timezone.utc)
                }
                mock_response.return_value = mock_profile_response
                
                profile_data = {
                    "full_name": "Updated User",
                    "phone": "0987654321",
                    "gender": "male",
                    "address": "123 Test St"
                }
                
                response = client.put("/api/v1/profile/update", json=profile_data)
                
                assert response.status_code == 200
                data = response.json()
                assert data["success"] is True
                assert "data" in data


class TestAPIErrorHandling:
    """Test API error handling"""
    
    def test_404_endpoint(self, client):
        """Test non-existent endpoint returns 404"""
        response = client.get("/api/v1/nonexistent")
        assert response.status_code == 404
    
    def test_method_not_allowed(self, client):
        """Test wrong HTTP method returns 405"""
        response = client.delete("/api/v1/auth/health")
        assert response.status_code == 405
    
    def test_invalid_json(self, client, override_get_db):
        """Test invalid JSON returns 422"""
        response = client.post(
            "/api/v1/auth/register",
            data="invalid json",
            headers={"Content-Type": "application/json"}
        )
        assert response.status_code == 422


if __name__ == "__main__":
    pytest.main([__file__, "-v"])
